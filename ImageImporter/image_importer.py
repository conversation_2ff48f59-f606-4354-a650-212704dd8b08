import cv2
import os
from pathlib import Path
from typing import List
import numpy as np

try:
    from pdf2image import convert_from_path

    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False


class FileImporter:
    """
    Zjednodušená třída pro import obr<PERSON><PERSON><PERSON>ů a PDF souborů.
    Zaměřuje se na základní funkcionalitu bez složitého správy souborů.
    """

    def __init__(self, folder_path: str):
        """
        Args:
            folder_path: Cesta ke složce s obrázky
        """
        self.folder_path = folder_path

        # Podporované formáty
        self.image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        if PDF_SUPPORT:
            self.supported_extensions = self.image_extensions + ['.pdf']
        else:
            self.supported_extensions = self.image_extensions

    def is_supported_file(self, filename: str) -> bool:
        """<PERSON><PERSON><PERSON>luje, zda je soubor podporovaný."""
        ext = os.path.splitext(filename.lower())[1]
        return ext in self.supported_extensions

    def load_image(self, filepath: str) -> np.ndarray:
        """
        Načte jeden obrázek pomocí OpenCV.

        Args:
            filepath: Cesta k souboru

        Returns:
            Numpy array s obrázkem nebo None při chybě
        """

        if not os.path.exists(filepath):
            print(f"Soubor neexistuje: {filepath}")
            return None

        img = cv2.imread(filepath, cv2.IMREAD_COLOR)
        if img is None:
            print(f"Nepodařilo se načíst obrázek: {filepath}")
            return None

        return img

    def load_pdf_pages(self, filepath: str) -> List[cv2.Mat]:
        """
        Načte stránky z PDF jako obrázky.

        Args:
            filepath: Cesta k PDF souboru

        Returns:
            Seznam numpy arrays s obrázky stránek
        """
        if not PDF_SUPPORT:
            print("PDF podpora není k dispozici. Nainstalujte pdf2image.")
            return []

        if not os.path.exists(filepath):
            print(f"Soubor neexistuje: {filepath}")
            return []

        try:
            # Převod PDF na obrázky
            pil_images = convert_from_path(filepath, dpi=200)

            cv_images = []
            for pil_img in pil_images:
                # Převod z PIL na OpenCV formát
                img_array = np.array(pil_img)
                # PIL používá RGB, OpenCV BGR
                if len(img_array.shape) == 3:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                cv_images.append(img_array)

            return cv_images

        except Exception as e:
            print(f"Chyba při načítání PDF {filepath}: {e}")
            return []

    def load_file(self, filepath: str) -> List[cv2.Mat]:
        """
        Načte soubor - obrázek vrátí jako jednoelementový seznam,
        PDF vrátí jako seznam stránek.

        Args:
            filepath: Cesta k souboru

        Returns:
            Seznam obrázků (numpy arrays)
        """
        #root_dir = Path(__file__).parent
        filepath = Path( self.folder_path, filepath)
        ext = os.path.splitext(filepath)[1].lower()

        if ext == '.pdf':
            return self.load_pdf_pages(filepath)
        elif ext in self.image_extensions:
            img = self.load_image(filepath)
            return [img] if img is not None else []
        else:
            print(f"Nepodporovaný formát: {ext}")
            return []

    def get_files_in_folder(self) -> List[str]:
        """
        Najde všechny podporované soubory ve složce.

        Returns:
            Seznam cest k podporovaným souborům
        """
        if not os.path.exists(self.folder_path):
            print(f"Složka neexistuje: {self.folder_path}")
            return []

        files = []
        for filename in os.listdir(self.folder_path):
            filepath = os.path.join(self.folder_path, filename)

            # Pouze soubory (ne složky)
            if os.path.isfile(filepath) and self.is_supported_file(filename):
                files.append(filepath)

        return sorted(files)

    def import_all_files(self) -> List[np.ndarray]:
        """
        Načte všechny podporované soubory ze složky.

        Returns:
            Seznam všech načtených obrázků
        """
        files = self.get_files_in_folder()

        if not files:
            print("Žádné podporované soubory nenalezeny.")
            return []

        all_images = []
        successful_files = 0

        for filepath in files:
            print(f"Načítám: {os.path.basename(filepath)}")

            images = self.load_file(filepath)

            if images:
                all_images.extend(images)
                successful_files += 1
                print(f"  ✓ Načteno {len(images)} obrázků")
            else:
                print(f"  ✗ Chyba při načítání")

        print(f"\nSouhrn: {successful_files}/{len(files)} souborů úspěšně načteno")
        print(f"Celkem obrázků: {len(all_images)}")

        return all_images


# Ukázka použití:
if __name__ == "__main__":
    # Vytvoření importéra
    importer = FileImporter("./hotfolder")

    # Import všech souborů
    images = importer.import_all_files()

    # Zpracování obrázků...
    for i, img in enumerate(images):
        print(f"Obrázek {i}: rozměry {img.shape}")