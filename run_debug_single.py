#!/usr/bin/env python3
"""
Skript pro spuštění debug pipeline na konkrétním PDF souboru.
Umožňuje nastavit vlastní parametry pro zpracování.
"""

import os
import sys
import cv2
import argparse
from pathlib import Path

# Přidání cesty k projektu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ImageImporter.image_importer import FileImporter
from pipeline_debug import PreprocessPipelineDebug


def parse_arguments():
    """Parsování argumentů příkazové řádky."""
    parser = argparse.ArgumentParser(
        description="Debug pipeline pro konkrétní PDF soubor",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "pdf_file", 
        help="Název PDF souboru v hotfolder (např. 'F1.pdf')"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        default="debug_single_output",
        help="Výstupní složka pro debug obrázky"
    )
    
    parser.add_argument(
        "--input-dpi",
        type=int,
        default=200,
        help="Vstupní DPI PDF souboru"
    )
    
    # Parametry pro denoising
    parser.add_argument("--denoise-h", type=int, default=10, help="Síla denoising filtru")
    parser.add_argument("--denoise-template-size", type=int, default=7, help="Velikost template okna pro denoising")
    parser.add_argument("--denoise-search-size", type=int, default=21, help="Velikost search okna pro denoising")
    
    # Parametry pro CLAHE
    parser.add_argument("--clahe-clip-limit", type=float, default=2.0, help="Clip limit pro CLAHE")
    parser.add_argument("--clahe-grid-size", type=int, default=8, help="Velikost gridu pro CLAHE")
    
    # Parametry pro Sauvola binarizaci
    parser.add_argument("--sauvola-window-size", type=int, default=25, help="Velikost okna pro Sauvola binarizaci")
    parser.add_argument("--sauvola-k", type=float, default=0.2, help="K parametr pro Sauvola")
    parser.add_argument("--sauvola-r", type=int, default=128, help="R parametr pro Sauvola")
    
    # Parametry pro mediánový filtr
    parser.add_argument("--median-kernel-size", type=int, default=3, help="Velikost kernelu pro mediánový filtr")
    
    # Parametry pro unsharp mask
    parser.add_argument("--apply-unsharp-mask", action="store_true", help="Aplikovat unsharp mask")
    parser.add_argument("--unsharp-kernel-size", type=int, default=5, help="Velikost kernelu pro unsharp mask")
    parser.add_argument("--unsharp-sigma", type=float, default=1.0, help="Sigma pro unsharp mask")
    parser.add_argument("--unsharp-amount", type=float, default=1.0, help="Amount pro unsharp mask")
    parser.add_argument("--unsharp-threshold", type=int, default=0, help="Threshold pro unsharp mask")
    
    # Parametry pro morfologické operace
    parser.add_argument("--apply-morphology", action="store_true", help="Aplikovat morfologické operace")
    parser.add_argument("--morph-kernel-size", type=int, default=2, help="Velikost kernelu pro morfologické operace")
    
    return parser.parse_args()


def main():
    """Hlavní funkce skriptu."""
    args = parse_arguments()
    
    print("🚀 Spouštím debug pipeline pro konkrétní PDF soubor")
    print("=" * 60)
    print(f"📄 Soubor: {args.pdf_file}")
    print(f"📁 Výstupní složka: {args.output_dir}")
    print("=" * 60)
    
    # Nastavení cest
    hotfolder_path = "hotfolder"
    
    # Kontrola existence souboru
    pdf_path = os.path.join(hotfolder_path, args.pdf_file)
    if not os.path.exists(pdf_path):
        print(f"❌ Soubor neexistuje: {pdf_path}")
        return 1
    
    # Vytvoření výstupní složky
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Načtení PDF pomocí FileImporter
    importer = FileImporter(hotfolder_path)
    
    try:
        images = importer.load_file(args.pdf_file)
        if not images:
            print(f"❌ Nepodařilo se načíst PDF: {args.pdf_file}")
            return 1
            
        print(f"✅ Načteno {len(images)} stránek z PDF")
        
        # Zpracování každé stránky
        for page_idx, image in enumerate(images):
            page_name = f"{Path(args.pdf_file).stem}_page_{page_idx + 1:02d}"
            print(f"\n📄 Zpracovávám stránku {page_idx + 1}/{len(images)}: {page_name}")
            
            # Vytvoření dočasného souboru pro debug pipeline
            temp_image_path = os.path.join(args.output_dir, f"{page_name}_temp.png")
            cv2.imwrite(temp_image_path, image)
            
            # Vytvoření debug pipeline
            debug_output_path = os.path.join(args.output_dir, f"{page_name}_debug")
            pipeline = PreprocessPipelineDebug(
                debug=True, 
                output_debug_path=debug_output_path
            )
            
            try:
                # Spuštění debug pipeline s parametry z argumentů
                result = pipeline.process(
                    temp_image_path,
                    input_dpi=args.input_dpi,
                    
                    # Parametry pro denoising
                    denoise_h=args.denoise_h,
                    denoise_template_size=args.denoise_template_size,
                    denoise_search_size=args.denoise_search_size,
                    
                    # Parametry pro CLAHE
                    clahe_clip_limit=args.clahe_clip_limit,
                    clahe_grid_size=args.clahe_grid_size,
                    
                    # Parametry pro Sauvola binarizaci
                    sauvola_window_size=args.sauvola_window_size,
                    sauvola_k=args.sauvola_k,
                    sauvola_r=args.sauvola_r,
                    
                    # Parametry pro mediánový filtr
                    median_kernel_size=args.median_kernel_size,
                    
                    # Parametry pro unsharp mask
                    apply_unsharp_mask=args.apply_unsharp_mask,
                    unsharp_kernel_size=args.unsharp_kernel_size,
                    unsharp_sigma=args.unsharp_sigma,
                    unsharp_amount=args.unsharp_amount,
                    unsharp_threshold=args.unsharp_threshold,
                    
                    # Parametry pro morfologické operace
                    apply_morphology=args.apply_morphology,
                    morph_kernel_size=args.morph_kernel_size
                )
                
                # Uložení finálního výsledku
                final_output_path = os.path.join(args.output_dir, f"{page_name}_final.png")
                cv2.imwrite(final_output_path, result)
                
                print(f"✅ Stránka zpracována úspěšně")
                print(f"   📁 Debug obrázky: {debug_output_path}")
                print(f"   🖼️  Finální výsledek: {final_output_path}")
                
            except Exception as e:
                print(f"❌ Chyba při zpracování stránky {page_idx + 1}: {e}")
                return 1
                
            finally:
                # Smazání dočasného souboru
                if os.path.exists(temp_image_path):
                    os.remove(temp_image_path)
        
        print(f"\n✅ Všechny stránky zpracovány úspěšně!")
        print(f"📁 Výsledky najdete v: {os.path.abspath(args.output_dir)}")
        return 0
        
    except Exception as e:
        print(f"❌ Chyba při načítání PDF {args.pdf_file}: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
