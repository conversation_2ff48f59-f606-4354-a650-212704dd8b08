import cv2

from ImageImporter.image_importer import FileImporter
from ImageProcessor.steps.step1_prepare import to_grayscale, tune_correct_perspective, normalize_to_300dpi, \
    tune_normalize_to_300dpi
from ImageProcessor.steps.step2_improve import clahe, tune_clahe, tune_auto_tone_mapping, tune_denoise, \
    tune_unsharp_mask, denoise, unsharp_mask
from ImageProcessor.steps.step3_clean import tune_sauvola_binarization
from ImageProcessor.steps.step4_remove_blobs import barcode_step


class StepTuner:
    def __init__(self, win_name, process_fn, params=None):
        """
        win_name   : j<PERSON>no okna
        process_fn : funkce (img, params_dict) -> vizualizace (BGR obraz)
        params     : NEPOVINNÉ. dict { param_name: (min, max, default) }.
                     Pokud není zadáno, vezme se z atributu funkce:
                     process_fn.__tunable_params__ nebo process_fn.tunable_params.
        """
        self.win_name = win_name
        self.process_fn = process_fn

        # Získání definice parametrů (min, max, default)
        src_params = (
            params
            or getattr(process_fn, "__tunable_params__", None)
            or getattr(process_fn, "tunable_params", None)
        )
        if not src_params:
            raise ValueError("Nebyla nalezena definice parametrů. Předáte params nebo nastavte __tunable_params__ na funkci.")

        # Normalizace do jednotného tvaru a typů
        # Uložíme trojice, min/max rozsahy a počáteční hodnoty
        self.triples = {}
        self.params = {}
        self.ranges = {}
        self.float_params = {}
        self.scales = {}

        for name, triple in src_params.items():
            if not (isinstance(triple, (tuple, list)) and len(triple) == 3):
                raise ValueError(f"Parametr '{name}' musí být trojice (min, max, default)")
            pmin, pmax, pdef = triple
            if pmax < pmin:
                raise ValueError(f"Parametr '{name}': max < min ({pmax} < {pmin})")

            is_float = any(isinstance(v, float) for v in (pmin, pmax, pdef))
            scale = 10 if is_float else 1  # rozlišení pro float parametry

            self.triples[name] = (pmin, pmax, pdef)
            self.params[name] = pdef
            self.ranges[name] = (pmin, pmax)
            self.float_params[name] = is_float
            self.scales[name] = scale

        # Vytvoření oken a sliderů (s posunem o min)
        cv2.namedWindow(win_name)
        for name, (pmin, pmax, pdef) in self.triples.items():
            scale = self.scales[name]
            # Ošetření degenerovaného rozsahu
            span = max(int(round((pmax - pmin) * scale)), 1)
            start = int(round((pdef) * scale))
            start = max(0, min(start, span))
            cv2.createTrackbar(name, win_name, start, span, self._on_change)

        # Inicializace parametrů z trackbarů
        self._on_change(0)

    def _on_change(self, _val):
        for name in self.params.keys():
            pos = cv2.getTrackbarPos(name, self.win_name)
            pmin, pmax = self.ranges[name]
            scale = self.scales[name]
            if self.float_params[name]:
                value = pmin + (pos / float(scale))
            else:
                value = int(pmin + pos)
            # Clamp na [min,max]
            if self.float_params[name]:
                value = max(min(value, pmax), pmin)
            else:
                value = max(min(int(value), int(pmax)), int(pmin))
            self.params[name] = value

    def run(self, img):
        print(f"Spouštím tuner s oknem: {self.win_name}")
        print(f"Obrázek: {img.shape}")
        print("Stiskněte ESC pro ukončení")

        frame_count = 0
        while True:
            vis = self.process_fn(img, self.params)
            cv2.imshow(self.win_name, vis)

            frame_count += 1
            if frame_count % 100 == 0:
                print(f"Frame {frame_count}, parametry: {self.params}")

            key = cv2.waitKey(50)
            if key == 27:  # ESC
                print("ESC stisknuto, ukončuji...")
                break
            elif key != -1:
                print(f"Stisknuta klávesa: {key}")

        cv2.destroyWindow(self.win_name)
        print("Okno zavřeno")

if __name__ == "__main__":
    importer = FileImporter("../hotfolder")
    try:
        img = importer.load_file("y.pdf")[0]
        img = to_grayscale(img)
        img = normalize_to_300dpi(img)
        img = tune_auto_tone_mapping(img, {"low_percent": 1.0, "high_percent": 99.0, "gamma": 1.1})
        img = to_grayscale(img)
        #img = denoise(img)
        #img = clahe(img)
        #img = denoise(img)
        #img = unsharp_mask(img)
        print(f"Načten obrázek s rozměry: {img.shape}")
    except Exception as e:
        print(f"Chyba při načítání obrázku: {e}")
        # Fallback - zkusíme jiný soubor
        try:
            img = importer.load_file("stamps.pdf")[0]
            img = to_grayscale(img)
            print(f"Načten fallback obrázek s rozměry: {img.shape}")
        except Exception as e2:
            print(f"Chyba i s fallback souborem: {e2}")
            raise RuntimeError("Žádný obrázek se nepodařilo načíst")

    if img is None or img.size == 0:
        raise RuntimeError("Obrázek je prázdný nebo nevalidní")

    # Parametry se nyní berou z atributu funkce barcode_step.__tunable_params__
    tuner = StepTuner("Barcode tuner", tune_denoise)
    tuner.run(img)
