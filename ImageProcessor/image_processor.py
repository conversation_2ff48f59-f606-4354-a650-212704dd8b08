import logging

logger = logging.getLogger(__name__)

from ImageProcessor.steps.step1_prepare import *
from ImageProcessor.steps.step2_improve import *
from ImageProcessor.steps.step3_clean import *
from ImageProcessor.steps.step4_remove_blobs import *

class ImageProcessor:
    def __init__(self):
        """Inicializace preprocessing pipeline (např. načtení filtrů)."""
        pass

    def run(self, image):
        """Hlavní metoda pro zpracování obrazu"""

        # 1. Převod na grayscale a normalizace DPI
        logger.info("Processing image")
        image = to_grayscale(image)
        image = normalize_to_300dpi(image)
        image = correct_perspective(image)
        cv2.imshow('image', image)
        cv2.waitKey(0)

        # 2. Tone mapping, CLAHE, USM, denoising
        image = auto_tone_mapping(image)
        image = clahe(image)
        image = denoise(image)  # před ostřením
        image = unsharp_mask(image)  # až po odšumu

        # 3. Binarizace a median filter
        image = sauvola_binarization(image)
        image = morphological_cleanup(image)

        # 4. Odstraněn<PERSON> ne-textových objektů
        # image = remove_barcodes_and_qrcodes(image)
        #image, filtered_objects = remove_non_text_objects(image)
        #visualize_detections(image, filtered_objects)

        return image
        pass

    def preprocess_image(self, image):
        """Preprocessing jednoho obrazu: Binarizace, denoising atd."""
        pass

    def batch_preprocess(self, images):
        """Batch verze: Preprocessing listu obrazů."""
        pass

    def rasterize_pdf(self, pdf_bytes):
        """Rasterizace PDF na obrazy pomocí Poppler (pdftoppm).."""
        # Příklad: subprocess.call(['pdftoppm', ...])
        pass

    def binarize_image(self, image):
        """Samostatná binarizace obrazu pro API endpoint. """
        pass