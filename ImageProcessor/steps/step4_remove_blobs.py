from typing import Tuple, List, Dict

import cv2
import numpy as np
import pyzbar

min_object_size = 400
max_text_aspect_ratio = 2.0


def barcode_step(img, p):
    # Parametry (rozšířené o nové)
    ksize = p.get("Sobel ksize", 1) | 1  # <PERSON>užij get pro bezpečné čtení
    blur_size = p.get("Blur size", 1) | 1
    mw, mh = p.get("Morph W", 11), p.get("Morph H", 7)  # Defaultní hodnoty pro lepší morfologii
    min_w, min_h = p.get("Min width", 100), p.get("Min height", 40)  # Min velikost pro filtraci
    max_w, max_h = p.get("Max width", 2000), p.get("Max height", 3000)  # Max velikost pro filtraci
    min_density = p.get("Min density", 0.8)  # Sníženo pro citlivěj<PERSON><PERSON> detekci
    aspect_min, aspect_max = p.get("Aspect min", 0.25), p.get("Aspect max", 24)  # <PERSON><PERSON><PERSON><PERSON> roz<PERSON>h pro rotace

    ksize = int(ksize) | 1
    blur_size = int(blur_size) | 1
    mw = max(1, int(mw))
    mh = max(1, int(mh))

    gray = img if img.ndim == 2 else cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    H, W = gray.shape[:2]

    gradX = cv2.Sobel(gray, cv2.CV_32F, 1, 0, ksize=ksize)
    gradX = cv2.convertScaleAbs(gradX)
    blurred = cv2.blur(gradX, (blur_size, blur_size))
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)



    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (mw, mh))
    morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    return morph

    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    vis = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    detected_count = 0

    for c in contours:
        x, y, w, h = cv2.boundingRect(c)
        # min/max rozměry
        if w < min_w or h < min_h or w > p.get("Max width", W) or h > p.get("Max height", H):
            continue
        # poměr stran
        aspect_ratio = (w / float(h)) if h > 0 else 0.0
        if not (aspect_min < aspect_ratio < aspect_max):
            continue
        # hustota v morfologické masce
        roi = morph[y:y+h, x:x+w]
        density = cv2.countNonZero(roi) / float(max(1, w*h))
        if density < p.get("Min density", 0.5):
            continue
        # vyřadit téměř celostránkové nebo přes okraje
        area_frac = (w*h) / float(W*H)
        if area_frac > 0.2:
            continue
        if (x <= 5 and x + w >= W - 5) or (y <= 5 and y + h >= H - 5):
            continue

        cv2.rectangle(vis, (x, y), (x + w, y + h), (0, 0, 255), 2)
        detected_count += 1

    print(f"[barcode_step] Detekováno {detected_count} čárových kódů")
    info_text = f"Detekováno: {detected_count} objektů"
    cv2.putText(vis, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

    # Parametry pro kontrolu v overlay
    param_text = f"ksize:{ksize} blur:{blur_size} morph:{mw}x{mh}"
    cv2.putText(vis, param_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    return vis


    # --- NOVÁ DETEKCE NA JINÝCH PRINCIPech (orientované hrany + morfologie ve 2 osách) ---
    # Zůstává kompatibilní s tunerem (stejné parametry), přidává volitelný "Min transitions".
    min_transitions = int(p.get("Min transitions", 10))

    # Zaručíme odstín šedi
    if img.ndim == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    H, W = gray.shape[:2]
    vis = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)

    # Debug: jednou za 20 cyklů
    if not hasattr(barcode_step, 'call_count'):
        barcode_step.call_count = 0
    barcode_step.call_count += 1
    debug_this_call = (barcode_step.call_count % 20 == 1)

    # Udržet liché velikosti kernelů
    blur_size = int(blur_size) | 1
    ksize = int(ksize) | 1
    mw = max(1, int(mw))
    mh = max(1, int(mh))

    def run_oriented_pass(axis: str):
        # axis: 'x' = vertikální hrany (typické 1D kódy s vertikálními čarami)
        #       'y' = horizontální hrany (pro případ otočení o 90°)
        if axis == 'x':
            grad = cv2.Sobel(gray, cv2.CV_32F, 1, 0, ksize=ksize)
            long_kernel = (max(3, mw), max(1, mh))  # horizontálně propojovat
            scan_axis = 'row'
        else:
            grad = cv2.Sobel(gray, cv2.CV_32F, 0, 1, ksize=ksize)
            long_kernel = (max(1, mh), max(3, mw))  # vertikálně propojovat
            scan_axis = 'col'

        grad = cv2.convertScaleAbs(grad)
        blurred = cv2.GaussianBlur(grad, (blur_size, blur_size), 0)
        _, bin_img = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

        # Morfologie: CLOSE (propojit čáry) + OPEN (odstranit šum)
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, long_kernel)
        mask = cv2.morphologyEx(bin_img, cv2.MORPH_CLOSE, kernel_close, iterations=1)
        kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel_open, iterations=1)

        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cands = []  # (x,y,w,h, score)
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)

            # Základní filtry
            if w < min_w or h < min_h or w > max_w or h > max_h:
                continue
            area = w * h
            area_frac = area / float(W * H)
            if area_frac > 0.15:
                continue
            spanX = (x <= 5 and x + w >= W - 5)
            spanY = (y <= 5 and y + h >= H - 5)
            if spanX or spanY:
                continue

            aspect = max(w, h) / max(1.0, min(w, h))
            if not (aspect_min < aspect < aspect_max):
                continue

            roi_mask = mask[y:y + h, x:x + w]
            density = cv2.countNonZero(roi_mask) / float(max(1, area))
            if density < min_density:
                continue

            # Přechody přes kratší osu (typická vlastnost čárových kódů)
            roi = gray[y:y + h, x:x + w]
            if scan_axis == 'row':
                row = roi[h // 2, :] if h > 0 else np.array([])
                transitions = int(np.sum(np.abs(np.diff(row > np.mean(row))) > 0)) if row.size > 0 else 0
            else:
                col = roi[:, w // 2] if w > 0 else np.array([])
                transitions = int(np.sum(np.abs(np.diff(col > np.mean(col))) > 0)) if col.size > 0 else 0
            if transitions < min_transitions:
                continue

            score = density + transitions / 100.0
            cands.append((x, y, w, h, score))

        return mask, cands

    _, cands_x = run_oriented_pass('x')
    _, cands_y = run_oriented_pass('y')
    boxes = cands_x + cands_y

    # NMS pro sloučení duplicit
    def nms(boxes, iou_thr=0.3):
        if not boxes:
            return []
        boxes_sorted = sorted(boxes, key=lambda b: b[4], reverse=True)
        kept = []
        for b in boxes_sorted:
            bx = (b[0], b[1], b[2], b[3])
            overl = False
            for k in kept:
                if _calculate_iou(bx, (k[0], k[1], k[2], k[3])) > iou_thr:
                    overl = True
                    break
            if not overl:
                kept.append(b)
        return kept

    kept = nms(boxes)

    # Vykreslení
    detected_count = 0
    for (x, y, w, h, score) in kept:
        cv2.rectangle(vis, (x, y), (x + w, y + h), (0, 0, 255), 3)
        detected_count += 1

    print(f"[barcode_step] Detekováno {detected_count} čárových kódů")
    info_text = f"Detekováno: {detected_count} objektů"
    cv2.putText(vis, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    param_text = f"ksize:{ksize} blur:{blur_size} morph:{mw}x{mh} minT:{min_transitions}"
    cv2.putText(vis, param_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    return vis

    # --- PŮVODNÍ IMPLEMENTACE (nepoužije se; zůstává pro případné srovnání) ---

    # Multi-scale detekce pomocí pyramid
    def detect_in_scale(image):
        # Gradienty pro x a y (pro vertikální i horizontální čáry)
        gradX = cv2.Sobel(image, cv2.CV_32F, 1, 0, ksize=ksize)
        gradY = cv2.Sobel(image, cv2.CV_32F, 0, 1, ksize=ksize)
        grad = cv2.magnitude(gradX, gradY)  # Kombinace pro lepší detekci
        grad = cv2.convertScaleAbs(grad)

        # Blur a Canny pro hranovou detekci
        blurred = cv2.GaussianBlur(grad, (blur_size, blur_size), 0)
        edges = cv2.Canny(blurred, 50, 150)  # Nastavitelné thresholdy pro Canny

        # Adaptivní threshold na edges pro binární masku
        thresh = cv2.adaptiveThreshold(edges, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 11, 2)

        # Morfologie: Opatrněji, aby se nespojilo vše do jedné kontury
        kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))  # Menší kernel
        morph = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel_open)

        # Menší kernel pro close, aby se nespojovaly vzdálené objekty
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (min(mw, 15), min(mh, 5)))
        morph = cv2.morphologyEx(morph, cv2.MORPH_CLOSE, kernel_close)

        # Najdi kontury
        contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return contours, morph

    # Pyramid: Detekce v originální velikosti a zmenšené (pro velké kódy)
    scales = [1.0, 0.5]  # Můžeš přidat více škál
    H, W = img.shape[:2]
    candidates = []  # seznam dvojic (kontura v originální velikosti, upsamplovaná maska morfologie)
    for scale in scales:
        scaled_img = cv2.resize(img, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
        contours, morph = detect_in_scale(scaled_img)
        # Upscale masky na originální velikost pro konzistentní výpočet density
        morph_up = cv2.resize(morph, (W, H), interpolation=cv2.INTER_NEAREST)
        # Upscale kontur zpět na originální velikost
        for c in contours:
            c_orig = (c / scale).astype(np.int32)  # Upscale
            candidates.append((c_orig, morph_up))

    # Filtrace a vizualizace na originálním obrázku
    result = img.copy()
    vis = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

    # Debug výpisy jen občas (každých 20 volání)
    if not hasattr(barcode_step, 'call_count'):
        barcode_step.call_count = 0
    barcode_step.call_count += 1

    debug_this_call = (barcode_step.call_count % 20 == 1)

    if debug_this_call:
        print(f"Celkem nalezeno {len(candidates)} kontur")
    detected_count = 0

    for i, (c, morph_mask) in enumerate(candidates):
        x, y, w, h = cv2.boundingRect(c)
        if debug_this_call:
            print(f"Kontura {i}: x={x}, y={y}, w={w}, h={h}")

        # Test velikosti
        if w < min_w or h < min_h or w > max_w or h > max_h:
            if debug_this_call:
                print(f"  -> Filtrováno velikostí: w={w} (min={min_w}, max={max_w}), h={h} (min={min_h}, max={max_h})")
            continue

        aspect_ratio = w / float(h) if h != 0 else 0
        if debug_this_call:
            print(f"  -> Aspect ratio: {aspect_ratio:.3f} (rozsah {aspect_min:.3f} - {aspect_max:.3f})")
        if not (aspect_min < aspect_ratio < aspect_max):
            if debug_this_call:
                print(f"  -> Filtrováno aspect ratio")
            continue

        # Vyřaď kandidáty, které pokrývají téměř celou stránku nebo přesahují oba okraje
        area_frac = (w * h) / float(W * H)
        # Hrany tolerujeme v pásmu 5 px
        border_spanning_x = (x <= 5 and x + w >= W - 5)
        border_spanning_y = (y <= 5 and y + h >= H - 5)
        too_big_abs = (w >= 0.9 * W and h >= 0.9 * H)
        if debug_this_call:
            print(f"  -> Area/border check: W={W},H={H}, area_frac={area_frac:.3f}, spanX={border_spanning_x}, spanY={border_spanning_y}, too_big={too_big_abs}")
        if area_frac > 0.2 or border_spanning_x or border_spanning_y or too_big_abs:
            if debug_this_call:
                print(f"  -> Filtrováno: přesahuje okraj/je příliš velké (area_frac={area_frac:.3f})")
            continue

        # Hustota (density) - počet nenulových pixelů v odpovídající masce
        roi_morph = morph_mask[y:y+h, x:x+w]
        denom = max(1, w * h)
        density = cv2.countNonZero(roi_morph) / denom
        if debug_this_call:
            print(f"  -> Density: {density:.3f} (min={min_density:.3f})")
        if density < min_density:
            if debug_this_call:
                print(f"  -> Filtrováno density")
            continue

        # Dodatečná kontrola: Počet přechodů (čar) v ROI pro ověření, že to vypadá jako čárový kód
        roi = img[y:y+h, x:x+w]
        row_sum = np.sum(roi, axis=0)  # Součet po sloupcích pro vertikální čáry
        transitions = np.sum(np.abs(np.diff(row_sum > np.mean(row_sum))) > 0)
        if debug_this_call:
            print(f"  -> Transitions: {transitions} (min=10)")
        if transitions < 10:  # Minimálně 10 přechodů (čar) pro platný kód
            if debug_this_call:
                print(f"  -> Filtrováno transitions")
            continue

        if debug_this_call:
            print(f"  -> ✓ DETEKOVÁNO!")

        # Nakresli červený obdélník
        cv2.rectangle(vis, (x, y), (x + w, y + h), (0, 0, 255), 3)

        # Přidej text s informacemi
        label = f"#{detected_count+1}: {w}x{h}"
        cv2.putText(vis, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        cv2.rectangle(result, (x, y), (x + w, y + h), 255, -1)
        detected_count += 1

    # Vždy vytiskni počet detekovaných kódů (pro porovnání s GUI)
    print(f"[barcode_step] Detekováno {detected_count} čárových kódů")

    # Přidej informace na obrázek
    info_text = f"Detekováno: {detected_count} objektů"
    cv2.putText(vis, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

    param_text = f"ksize:{ksize} blur:{blur_size} morph:{mw}x{mh}"
    cv2.putText(vis, param_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    return vis


# Tunable parameters for StepTuner (min, max, default)
barcode_step.__tunable_params__ = {
    "Sobel ksize": (1, 15, 0),
    "Blur size": (3, 31, 0),
    "Morph W": (1, 100, 7),
    "Morph H": (1, 100, 0),
    "Min width": (1, 1000, 0),
    "Min height": (1, 1000, 0),
    "Max width": (50, 8000, 2500),
    "Max height": (50, 8000, 3000),
    "Min density": (0.0, 1.5, 0.8),
    "Aspect min": (0.01, 1.0, 0.25),
    "Aspect max": (0.5, 100.0, 2.4),
}

def remove_barcodes_and_qrcodes(binary_img: np.ndarray) -> np.ndarray:
    """
    Odstraní čárové a QR kódy z binarizovaného obrazu.
    Vstup: binární obraz (0 = černá, 255 = bílá)
    Výstup: kopie obrazu, kde nalezené kódy jsou vyplněné bílou barvou.
    """
    # Zkopírujeme obraz pro výsledek
    result = binary_img.copy()

    # --- Detekce čárových kódů ---
    # Gradient v X (čárový kód = vertikální hrany)
    gradX = cv2.Sobel(binary_img, ddepth=cv2.CV_32F, dx=1, dy=0, ksize=-1)
    gradX = cv2.convertScaleAbs(gradX)

    # Vyhlazení a binarizace
    blurred = cv2.blur(gradX, (9, 9))
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

    # Morfologie pro spojení čar
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (21, 7))
    morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    # Najdi kontury
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for c in contours:
        x, y, w, h = cv2.boundingRect(c)
        # Heuristika – čárový/QR kód nebývá úplně titěrný
        if w > 50 and h > 50:
            cv2.rectangle(result, (x, y), (x+w, y+h), 255, -1)

    # --- Detekce QR kódů ---
    qrDecoder = cv2.QRCodeDetector()
    ok, points = qrDecoder.detect(binary_img)
    if ok and points is not None:
        # points je 4x2 pole vrcholů, vezmeme bounding box
        points = points[0] if points.ndim == 3 else points
        rect = cv2.boundingRect(points.astype(int))
        x, y, w, h = rect
        cv2.rectangle(result, (x, y), (x+w, y+h), 255, -1)

    return result

def remove_non_text_objects(binary_image: np.ndarray) -> Tuple[np.ndarray, List[Dict]]:
    """
    Hlavní metoda pro detekci a odstranění ne-textových objektů.

    Args:
        binary_image: Binarizovaný obraz (0=pozadí, 255=text)

    Returns:
        Tuple[vyčištěný obraz, seznam detekovaných objektů]
    """
    if len(binary_image.shape) != 2:
        raise ValueError("Vstupní obraz musí být binarizovaný (2D)")

    result_image = binary_image.copy()
    detected_objects = []

    # Detekce různých typů objektů

    # qr_objects = _detect_qr_codes(binary_image)
    # detected_objects.extend(qr_objects)
    #
    # barcode_objects =_detect_barcodes(binary_image)
    # detected_objects.extend(barcode_objects)

    #logo_objects = _detect_logos(binary_image)
    #detected_objects.extend(logo_objects)

    #stamp_objects = _detect_stamps(binary_image)
    #detected_objects.extend(stamp_objects)

    # Obecná detekce velkých ne-textových objektů
    generic_objects = _detect_generic_non_text_objects(binary_image)
    detected_objects.extend(generic_objects)

    # Odstranění překrývajících se detekcí
    filtered_objects = _filter_overlapping_detections(detected_objects)

    # Aplikace odstranění na obraz
    result_image = _apply_object_removal(binary_image, filtered_objects)

    print(f"Detekováno a odstraněno {len(filtered_objects)} ne-textových objektů")

    return result_image, filtered_objects


def _detect_qr_codes( binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce QR kódů pomocí pyzbar a morfologických operací.
    """
    print("Detekce QR kódů...")
    qr_objects = []

    try:
        # Pyzbar detekce
        decoded_objects = pyzbar.decode(binary_image)

        for obj in decoded_objects:
            if obj.type == 'QRCODE':
                # Získání bounding box
                points = obj.polygon
                if len(points) == 4:
                    x_coords = [p.x for p in points]
                    y_coords = [p.y for p in points]

                    x, y = min(x_coords), min(y_coords)
                    w = max(x_coords) - x
                    h = max(y_coords) - y

                    # Rozšíření bounding boxu o padding
                    padding = 10
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(binary_image.shape[1] - x, w + 2 * padding)
                    h = min(binary_image.shape[0] - y, h + 2 * padding)

                    qr_objects.append({
                        'type': 'QR_CODE',
                        'bbox': (x, y, w, h),
                        'confidence': 1.0,
                        'data': obj.data.decode('utf-8')
                    })

    except Exception as e:
        print(f"Chyba při pyzbar detekci: {e}")

    # Backup detekce pomocí morfologických operací (pro poškozené QR kódy)
    morphological_qr = _detect_qr_morphological(binary_image)
    qr_objects.extend(morphological_qr)

    return qr_objects


def _detect_qr_morphological(binary_image: np.ndarray) -> List[Dict]:
    """
    Backup detekce QR kódů pomocí morfologických operací.
    """
    qr_objects = []

    # Morfologické operace pro detekci čtvercových struktur
    kernel_size = max(3, min(binary_image.shape) // 100)
    kernel = np.ones((kernel_size, kernel_size), np.uint8)

    # Closing pro spojení fragmentů
    closed = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)

    # Detekce kontur
    contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        # Aproximace kontury
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # Kontrola čtvercovitosti
        if len(approx) >= 4:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            area = cv2.contourArea(contour)
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            # QR kód charakteristiky: čtvercový, středně velký, kompaktní
            if (0.7 <= aspect_ratio <= 1.3 and  # čtvercový
                    area > 1000 and area < binary_image.size * 0.1 and  # rozumná velikost
                    fill_ratio > 0.3):  # dostatečně vyplněný

                # Kontrola komplexity (QR kódy mají vysokou komplexitu)
                roi = binary_image[y:y + h, x:x + w]
                if _calculate_complexity(roi) > 0.1:
                    qr_objects.append({
                        'type': 'QR_CODE_MORPHOLOGICAL',
                        'bbox': (x, y, w, h),
                        'confidence': 0.7,
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })

    return qr_objects


def _detect_barcodes(binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce čárových kódů.
    """
    print("Detekce čárových kódů...")
    barcode_objects = []

    try:
        # Pyzbar detekce všech typů čárových kódů
        decoded_objects = pyzbar.decode(binary_image)

        for obj in decoded_objects:
            if obj.type != 'QRCODE':  # Všechno kromě QR kódů
                points = obj.polygon
                if len(points) >= 4:
                    x_coords = [p.x for p in points]
                    y_coords = [p.y for p in points]

                    x, y = min(x_coords), min(y_coords)
                    w = max(x_coords) - x
                    h = max(y_coords) - y

                    # Rozšíření bounding boxu
                    padding = 5
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(binary_image.shape[1] - x, w + 2 * padding)
                    h = min(binary_image.shape[0] - y, h + 2 * padding)

                    barcode_objects.append({
                        'type': f'BARCODE_{obj.type}',
                        'bbox': (x, y, w, h),
                        'confidence': 1.0,
                        'data': obj.data.decode('utf-8')
                    })

    except Exception as e:
        print(f"Chyba při detekci čárových kódů: {e}")

    # Morfologická detekce (backup)
    morphological_barcodes = _detect_barcodes_morphological(binary_image)
    barcode_objects.extend(morphological_barcodes)

    return barcode_objects


def _detect_barcodes_morphological(binary_image: np.ndarray) -> List[Dict]:
    """
    Morfologická detekce čárových kódů.
    """
    barcode_objects = []

    # Detekce vertikálních čar (typické pro čárové kódy)
    kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
    vertical_lines = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel_vertical)

    # Horizontální spojení čar
    kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
    barcode_mask = cv2.morphologyEx(vertical_lines, cv2.MORPH_CLOSE, kernel_horizontal)

    # Detekce kontur
    contours, _ = cv2.findContours(barcode_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        area = cv2.contourArea(contour)

        # Čárový kód charakteristiky: obdélníkový, horizontální
        if (aspect_ratio > 3 and  # široký
                area > 500 and area < binary_image.size * 0.05 and  # rozumná velikost
                h > 10 and h < 100):  # rozumná výška

            barcode_objects.append({
                'type': 'BARCODE_MORPHOLOGICAL',
                'bbox': (x, y, w, h),
                'confidence': 0.6,
                'area': area,
                'aspect_ratio': aspect_ratio
            })

    return barcode_objects


def _detect_logos(binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce log pomocí analýzy tvarů a komplexity.
    """
    print("Detekce log...")
    logo_objects = []

    # Morfologické operace pro spojení fragmentů log
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    cleaned = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)

    # Detekce kontur
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)

        # Logo charakteristiky: kompaktní, středně velká, komplexní
        if area > 800 and area < binary_image.size * 0.08:
            aspect_ratio = w / h if h > 0 else 0

            # Výpočet kompaktnosti (perimeter^2 / area)
            perimeter = cv2.arcLength(contour, True)
            compactness = (perimeter * perimeter) / area if area > 0 else float('inf')

            # Analýza komplexity ROI
            roi = binary_image[y:y + h, x:x + w]
            complexity = _calculate_complexity(roi)

            # Logo charakteristiky
            if (0.3 <= aspect_ratio <= 3.0 and  # ne příliš extrémní poměr
                    compactness < 50 and  # rozumně kompaktní
                    complexity > 0.05):  # dostatečně komplexní

                logo_objects.append({
                    'type': 'LOGO',
                    'bbox': (x, y, w, h),
                    'confidence': 0.8,
                    'area': area,
                    'complexity': complexity,
                    'compactness': compactness
                })

    return logo_objects


def _detect_stamps(binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce razítek (obvykle kruhová nebo obdélníková).
    """
    print("Detekce razítek...")
    stamp_objects = []

    # Detekce kruhových razítek pomocí Hough Transform
    #circular_stamps = _detect_circular_stamps(binary_image)
    #stamp_objects.extend(circular_stamps)

    # Detekce obdélníkových razítek
    rectangular_stamps = _detect_rectangular_stamps(binary_image)
    stamp_objects.extend(rectangular_stamps)

    return stamp_objects


def _detect_circular_stamps(binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce kruhových razítek pomocí Hough Transform.
    """
    stamps = []

    # Hough Circle Transform
    circles = cv2.HoughCircles(
        binary_image,
        cv2.HOUGH_GRADIENT,
        dp=1,
        minDist=30,
        param1=50,
        param2=30,
        minRadius=100,
        maxRadius=min(binary_image.shape) // 4
    )

    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")

        for (x, y, r) in circles:
            # Kontrola, zda kruh obsahuje dostatek pixelů (razítko)
            mask = np.zeros(binary_image.shape, dtype=np.uint8)
            cv2.circle(mask, (x, y), r, 255, -1)

            roi_pixels = binary_image[mask == 255]
            fill_ratio = np.sum(roi_pixels == 255) / len(roi_pixels)

            if 0.1 <= fill_ratio <= 0.8:  # rozumné vyplnění
                bbox_x = max(0, x - r)
                bbox_y = max(0, y - r)
                bbox_w = min(binary_image.shape[1] - bbox_x, 2 * r)
                bbox_h = min(binary_image.shape[0] - bbox_y, 2 * r)

                stamps.append({
                    'type': 'CIRCULAR_STAMP',
                    'bbox': (bbox_x, bbox_y, bbox_w, bbox_h),
                    'confidence': 0.9,
                    'center': (x, y),
                    'radius': r,
                    'fill_ratio': fill_ratio
                })

    return stamps


def _detect_rectangular_stamps(binary_image: np.ndarray) -> List[Dict]:
    """
    Detekce obdélníkových razítek.
    """
    stamps = []

    # Detekce silných hran (razítka mají často silné obrysy)
    edges = cv2.Canny(binary_image, 50, 150)

    # Dilatace hran
    kernel = np.ones((3, 3), np.uint8)
    edges_dilated = cv2.dilate(edges, kernel, iterations=1)

    # Detekce kontur
    contours, _ = cv2.findContours(edges_dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        # Aproximace obdélníkem
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        if len(approx) >= 4:
            x, y, w, h = cv2.boundingRect(contour)
            area = cv2.contourArea(contour)

            # Razítko charakteristiky
            if (area > 1000 and area < binary_image.size * 0.15 and
                    w > 100 and h > 100):

                aspect_ratio = w / h if h > 0 else 0

                # Kontrola hraničního poměru (razítka mají silné obrysy)
                roi = binary_image[y:y + h, x:x + w]
                edge_pixels = np.sum(cv2.Canny(roi, 50, 150) == 255)
                edge_ratio = edge_pixels / (w * h)

                if (0.5 <= aspect_ratio <= 2.0 and  # rozumný poměr stran
                        edge_ratio > 0.05):  # dostatek hran

                    stamps.append({
                        'type': 'RECTANGULAR_STAMP',
                        'bbox': (x, y, w, h),
                        'confidence': 0.7,
                        'area': area,
                        'edge_ratio': edge_ratio
                    })

    return stamps


def _detect_generic_non_text_objects(binary_image: np.ndarray) -> List[Dict]:
    """
    Obecná detekce velkých ne-textových objektů pomocí charakteristik.
    """
    print("Detekce obecných ne-textových objektů...")
    objects = []

    # Connected components analysis
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
        binary_image, connectivity=8)

    for i in range(1, num_labels):  # Skip background
        x, y, w, h, area = stats[i]

        if area > min_object_size:
            aspect_ratio = w / h if h > 0 else 0

            # Ne-textové charakteristiky
            if (aspect_ratio > max_text_aspect_ratio or  # příliš široký
                    aspect_ratio < 1 / max_text_aspect_ratio or  # příliš vysoký
                    area > binary_image.size * 0.1):  # příliš velký

                # Kontrola komplexity
                roi = binary_image[y:y + h, x:x + w]
                complexity = _calculate_complexity(roi)

                objects.append({
                    'type': 'GENERIC_NON_TEXT',
                    'bbox': (x, y, w, h),
                    'confidence': 0.5,
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'complexity': complexity
                })

    return objects


def _calculate_complexity(roi: np.ndarray) -> float:
    """
    Výpočet komplexity oblasti (poměr hran k celkové ploše).
    """
    if roi.size == 0:
        return 0

    edges = cv2.Canny(roi, 50, 150)
    edge_pixels = np.sum(edges == 255)
    total_pixels = roi.size

    return edge_pixels / total_pixels


def _filter_overlapping_detections(detections: List[Dict]) -> List[Dict]:
    """
    Odstranění překrývajících se detekcí (NMS-like filtrování).
    """
    if not detections:
        return []

    # Seřazení podle confidence
    sorted_detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
    filtered = []

    for detection in sorted_detections:
        bbox1 = detection['bbox']
        is_overlapping = False

        for existing in filtered:
            bbox2 = existing['bbox']

            # Výpočet IoU (Intersection over Union)
            iou = _calculate_iou(bbox1, bbox2)

            if iou > 0.3:  # 30% overlap threshold
                is_overlapping = True
                break

        if not is_overlapping:
            filtered.append(detection)

    return filtered


def _calculate_iou(bbox1: Tuple[int, int, int, int],
                   bbox2: Tuple[int, int, int, int]) -> float:
    """
    Výpočet Intersection over Union pro dva bounding boxy.
    """
    x1, y1, w1, h1 = bbox1
    x2, y2, w2, h2 = bbox2

    # Souřadnice průniku
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)

    if x_right < x_left or y_bottom < y_top:
        return 0.0

    # Plocha průniku
    intersection_area = (x_right - x_left) * (y_bottom - y_top)

    # Plocha sjednocení
    bbox1_area = w1 * h1
    bbox2_area = w2 * h2
    union_area = bbox1_area + bbox2_area - intersection_area

    return intersection_area / union_area if union_area > 0 else 0.0


def _apply_object_removal(binary_image: np.ndarray,
                          objects: List[Dict]) -> np.ndarray:
    """
    Aplikace odstranění detekovaných objektů z obrazu.
    """
    result = binary_image.copy()

    for obj in objects:
        x, y, w, h = obj['bbox']

        # Vymazání oblasti (nastavení na 0 = pozadí)
        result[y:y + h, x:x + w] = 0

    return result


def visualize_detections(binary_image: np.ndarray,
                         detections: List[Dict]) -> np.ndarray:
    """
    Vizualizace detekovaných objektů pro debugging.
    """
    vis_img = cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR)

    colors = {
        'QR_CODE': (255, 0, 0),
        'QR_CODE_MORPHOLOGICAL': (200, 0, 0),
        'BARCODE': (0, 255, 0),
        'BARCODE_MORPHOLOGICAL': (0, 200, 0),
        'LOGO': (0, 0, 255),
        'CIRCULAR_STAMP': (255, 255, 0),
        'RECTANGULAR_STAMP': (255, 0, 255),
        'GENERIC_NON_TEXT': (128, 128, 128)
    }

    for detection in detections:
        x, y, w, h = detection['bbox']
        obj_type = detection['type']

        # Určení barvy podle typu
        color = colors.get(obj_type.split('_')[0], (0, 255, 255))

        # Kreslení bounding boxu
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 2)

        # Popisek
        label = f"{obj_type[:10]}:{detection['confidence']:.2f}"
        cv2.putText(vis_img, label, (x, y - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    return vis_img