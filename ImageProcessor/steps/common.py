import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
from sklearn.cluster import DBSCAN
from scipy import ndimage
import matplotlib.pyplot as plt

def _ensure_odd(n: int) -> int:
    return n if n % 2 == 1 else n + 1


class AlignedLineDetector:
    """
    Specializovaný detektor horizontálních a vertikálních linií
    pro vyrovnané dokumenty (účtenky). Vrací přesné souřadnice linií.
    """

    def __init__(self,
                 angle_tolerance: float = 2.0,
                 min_line_length: int = 300,
                 line_thickness_tolerance: int = 3,
                 merge_distance: int = 5,
                 debug: bool = False):
        """
        Args:
            angle_tolerance: Tolerance úhlu pro horizontální/vertikální klasifikaci
            min_line_length: Minimální délka linie v pixelech
            line_thickness_tolerance: Tolerance tloušťky linie
            merge_distance: Vzdálenost pro slučování blízk<PERSON>ch linií
            debug: Debug vizualizace
        """
        self.angle_tolerance = angle_tolerance
        self.min_line_length = min_line_length
        self.thickness_tolerance = line_thickness_tolerance
        self.merge_distance = merge_distance
        self.debug = debug

        if debug:
            logging.basicConfig(level=logging.DEBUG)

    def detect_lines(self, binary_image: np.ndarray) -> Dict:
        """
        Hlavní metoda pro detekci linií ve vyrovnaném obraze.

        Args:
            binary_image: Vyrovnaný binarizovaný obraz (0=pozadí, 255=linie/text)

        Returns:
            Dict s koordináty horizontálních a vertikálních linií
        """
        if len(binary_image.shape) != 2:
            raise ValueError("Vstupní obraz musí být binarizovaný (2D)")

        height, width = binary_image.shape

        # Fáze 1: Detekce horizontálních linií
        horizontal_lines = self._detect_horizontal_lines(binary_image)

        # Fáze 2: Detekce vertikálních linií
        vertical_lines = self._detect_vertical_lines(binary_image)

        # Fáze 3: Post-processing a čištění
        filtered_horizontal = self._filter_and_merge_lines(horizontal_lines, 'horizontal', width)
        filtered_vertical = self._filter_and_merge_lines(vertical_lines, 'vertical', height)

        # Fáze 4: Detekce průsečíků
        intersections = self._find_line_intersections(filtered_horizontal, filtered_vertical)

        result = {
            'horizontal_lines': filtered_horizontal,
            'vertical_lines': filtered_vertical,
            'intersections': intersections,
            'image_dimensions': (width, height),
            'stats': {
                'horizontal_count': len(filtered_horizontal),
                'vertical_count': len(filtered_vertical),
                'intersection_count': len(intersections)
            }
        }

        logging.debug(f"Detekováno {len(filtered_horizontal)} horizontálních "
                      f"a {len(filtered_vertical)} vertikálních linií")

        return result

    def _detect_horizontal_lines(self, binary_image: np.ndarray) -> List[Dict]:
        """
        Detekce horizontálních linií pomocí morfologických operací a projekce.
        """
        logging.debug("Detekce horizontálních linií...")

        # Morfologická detekce horizontálních struktur
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        horizontal_mask = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, horizontal_kernel)

        # Odstranění příliš tenkých nebo tlustých struktur
        cleaned_horizontal = self._clean_line_mask(horizontal_mask, 'horizontal')

        # Analýza pomocí horizontální projekce
        horizontal_projection = np.sum(cleaned_horizontal, axis=1)

        # Detekce peaků v projekci (řádky s liniemi)
        line_rows = self._find_projection_peaks(horizontal_projection, 'horizontal')

        # Extrakce přesných souřadnic pro každý řádek
        horizontal_lines = []
        for row in line_rows:
            line_segments = self._extract_line_segments_horizontal(cleaned_horizontal, row)
            horizontal_lines.extend(line_segments)

        return horizontal_lines

    def _detect_vertical_lines(self, binary_image: np.ndarray) -> List[Dict]:
        """
        Detekce vertikálních linií pomocí morfologických operací a projekce.
        """
        logging.debug("Detekce vertikálních linií...")

        # Morfologická detekce vertikálních struktur
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
        vertical_mask = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, vertical_kernel)

        # Odstranění příliš tenkých nebo tlustých struktur
        cleaned_vertical = self._clean_line_mask(vertical_mask, 'vertical')

        # Analýza pomocí vertikální projekce
        vertical_projection = np.sum(cleaned_vertical, axis=0)

        # Detekce peaků v projekci (sloupce s liniemi)
        line_columns = self._find_projection_peaks(vertical_projection, 'vertical')

        # Extrakce přesných souřadnic pro každý sloupec
        vertical_lines = []
        for col in line_columns:
            line_segments = self._extract_line_segments_vertical(cleaned_vertical, col)
            vertical_lines.extend(line_segments)

        return vertical_lines

    def _clean_line_mask(self, mask: np.ndarray, orientation: str) -> np.ndarray:
        """
        Čištění masky linií - odstranění šumu a nelineárních struktur.
        """
        cleaned = mask.copy()

        # Odstranění malých objektů
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)

        for i in range(1, num_labels):
            x, y, w, h, area = stats[i]

            # Kontrola aspect ratio a velikosti
            if orientation == 'horizontal':
                aspect_ratio = w / h if h > 0 else 0
                min_length = self.min_line_length
                valid = aspect_ratio > 3 and w >= min_length and h <= 10
            else:  # vertical
                aspect_ratio = h / w if w > 0 else 0
                min_length = self.min_line_length
                valid = aspect_ratio > 3 and h >= min_length and w <= 10

            if not valid:
                # Odstranění nevalidního objektu
                cleaned[labels == i] = 0

        return cleaned

    def _find_projection_peaks(self, projection: np.ndarray, orientation: str) -> List[int]:
        """
        Nalezení peaků v projekci odpovídajících liniím.
        """
        # Prah pro detekci linií (adaptivní podle obsahu)
        mean_projection = np.mean(projection)
        std_projection = np.std(projection)
        threshold = mean_projection + 0.5 * std_projection

        # Najdi indexy překračující prah
        peak_indices = np.where(projection > threshold)[0]

        if len(peak_indices) == 0:
            return []

        # Seskupení sousedních indexů (spojité linie)
        groups = []
        current_group = [peak_indices[0]]

        for i in range(1, len(peak_indices)):
            if peak_indices[i] - peak_indices[i - 1] <= self.thickness_tolerance:
                current_group.append(peak_indices[i])
            else:
                if len(current_group) > 0:
                    groups.append(current_group)
                current_group = [peak_indices[i]]

        if len(current_group) > 0:
            groups.append(current_group)

        # Střed každé skupiny představuje pozici linie
        line_positions = []
        for group in groups:
            center_pos = int(np.mean(group))
            # Validace pomocí hodnoty projekce
            if projection[center_pos] > threshold:
                line_positions.append(center_pos)

        return line_positions

    def _extract_line_segments_horizontal(self, mask: np.ndarray, row: int) -> List[Dict]:
        """
        Extrakce segmentů horizontální linie v daném řádku.
        """
        segments = []
        row_data = mask[row, :]

        # Najdi spojité segmenty (běhy jedniček)
        in_segment = False
        segment_start = None

        for x in range(len(row_data)):
            if row_data[x] == 255:  # pixel linie
                if not in_segment:
                    segment_start = x
                    in_segment = True
            else:  # pixel pozadí
                if in_segment:
                    segment_end = x - 1
                    segment_length = segment_end - segment_start + 1

                    if segment_length >= self.min_line_length:
                        segments.append({
                            'type': 'horizontal',
                            'coordinates': {
                                'start_point': (segment_start, row),
                                'end_point': (segment_end, row),
                                'y_position': row,
                                'x_start': segment_start,
                                'x_end': segment_end
                            },
                            'length': segment_length,
                            'thickness': self._measure_line_thickness(mask, segment_start, segment_end, row,
                                                                      'horizontal')
                        })

                    in_segment = False

        # Kontrola posledního segmentu
        if in_segment:
            segment_end = len(row_data) - 1
            segment_length = segment_end - segment_start + 1

            if segment_length >= self.min_line_length:
                segments.append({
                    'type': 'horizontal',
                    'coordinates': {
                        'start_point': (segment_start, row),
                        'end_point': (segment_end, row),
                        'y_position': row,
                        'x_start': segment_start,
                        'x_end': segment_end
                    },
                    'length': segment_length,
                    'thickness': self._measure_line_thickness(mask, segment_start, segment_end, row, 'horizontal')
                })

        return segments

    def _extract_line_segments_vertical(self, mask: np.ndarray, col: int) -> List[Dict]:
        """
        Extrakce segmentů vertikální linie v daném sloupci.
        """
        segments = []
        col_data = mask[:, col]

        # Najdi spojité segmenty (běhy jedniček)
        in_segment = False
        segment_start = None

        for y in range(len(col_data)):
            if col_data[y] == 255:  # pixel linie
                if not in_segment:
                    segment_start = y
                    in_segment = True
            else:  # pixel pozadí
                if in_segment:
                    segment_end = y - 1
                    segment_length = segment_end - segment_start + 1

                    if segment_length >= self.min_line_length:
                        segments.append({
                            'type': 'vertical',
                            'coordinates': {
                                'start_point': (col, segment_start),
                                'end_point': (col, segment_end),
                                'x_position': col,
                                'y_start': segment_start,
                                'y_end': segment_end
                            },
                            'length': segment_length,
                            'thickness': self._measure_line_thickness(mask, segment_start, segment_end, col, 'vertical')
                        })

                    in_segment = False

        # Kontrola posledního segmentu
        if in_segment:
            segment_end = len(col_data) - 1
            segment_length = segment_end - segment_start + 1

            if segment_length >= self.min_line_length:
                segments.append({
                    'type': 'vertical',
                    'coordinates': {
                        'start_point': (col, segment_start),
                        'end_point': (col, segment_end),
                        'x_position': col,
                        'y_start': segment_start,
                        'y_end': segment_end
                    },
                    'length': segment_length,
                    'thickness': self._measure_line_thickness(mask, segment_start, segment_end, col, 'vertical')
                })

        return segments

    def _measure_line_thickness(self, mask: np.ndarray, start: int, end: int,
                                position: int, orientation: str) -> int:
        """
        Měření tloušťky linie v daném segmentu.
        """
        if orientation == 'horizontal':
            # Měř tloušťku ve směru y
            center_x = (start + end) // 2

            # Najdi rozsah pixelů ve směru y
            thickness = 0
            for offset in range(-10, 11):  # kontrola +/- 10 pixelů
                y = position + offset
                if (0 <= y < mask.shape[0] and
                        center_x < mask.shape[1] and
                        mask[y, center_x] == 255):
                    thickness += 1
        else:  # vertical
            # Měř tloušťku ve směru x
            center_y = (start + end) // 2

            # Najdi rozsah pixelů ve směru x
            thickness = 0
            for offset in range(-10, 11):  # kontrola +/- 10 pixelů
                x = position + offset
                if (0 <= x < mask.shape[1] and
                        center_y < mask.shape[0] and
                        mask[center_y, x] == 255):
                    thickness += 1

        return max(1, thickness)

    def _filter_and_merge_lines(self, lines: List[Dict], orientation: str,
                                max_dimension: int) -> List[Dict]:
        """
        Filtrování a slučování blízkých linií.
        """
        if not lines:
            return []

        # Seřazení podle pozice
        if orientation == 'horizontal':
            lines.sort(key=lambda x: x['coordinates']['y_position'])
            position_key = 'y_position'
        else:
            lines.sort(key=lambda x: x['coordinates']['x_position'])
            position_key = 'x_position'

        # Slučování blízkých linií
        merged_lines = []
        i = 0

        while i < len(lines):
            current_line = lines[i]
            lines_to_merge = [current_line]

            # Najdi všechny blízké linie
            j = i + 1
            while j < len(lines):
                distance = abs(lines[j]['coordinates'][position_key] -
                               current_line['coordinates'][position_key])

                if distance <= self.merge_distance:
                    lines_to_merge.append(lines[j])
                    j += 1
                else:
                    break

            # Sloučí linie do jedné
            if len(lines_to_merge) == 1:
                merged_lines.append(current_line)
            else:
                merged_line = self._merge_line_group(lines_to_merge, orientation)
                merged_lines.append(merged_line)

            i = j

        # Finální filtrace podle délky a pozice
        filtered_lines = []
        for line in merged_lines:
            if (line['length'] >= self.min_line_length and
                    self._is_valid_line_position(line, max_dimension)):
                filtered_lines.append(line)

        return filtered_lines

    def _merge_line_group(self, lines: List[Dict], orientation: str) -> Dict:
        """
        Sloučení skupiny blízkých linií do jedné reprezentativní linie.
        """
        if orientation == 'horizontal':
            # Střední y-pozice
            avg_y = int(np.mean([line['coordinates']['y_position'] for line in lines]))

            # Celkový x-rozsah
            min_x = min([line['coordinates']['x_start'] for line in lines])
            max_x = max([line['coordinates']['x_end'] for line in lines])

            # Průměrná tloušťka
            avg_thickness = int(np.mean([line['thickness'] for line in lines]))

            return {
                'type': 'horizontal',
                'coordinates': {
                    'start_point': (min_x, avg_y),
                    'end_point': (max_x, avg_y),
                    'y_position': avg_y,
                    'x_start': min_x,
                    'x_end': max_x
                },
                'length': max_x - min_x + 1,
                'thickness': avg_thickness,
                'merged_from': len(lines)
            }
        else:  # vertical
            # Střední x-pozice
            avg_x = int(np.mean([line['coordinates']['x_position'] for line in lines]))

            # Celkový y-rozsah
            min_y = min([line['coordinates']['y_start'] for line in lines])
            max_y = max([line['coordinates']['y_end'] for line in lines])

            # Průměrná tloušťka
            avg_thickness = int(np.mean([line['thickness'] for line in lines]))

            return {
                'type': 'vertical',
                'coordinates': {
                    'start_point': (avg_x, min_y),
                    'end_point': (avg_x, max_y),
                    'x_position': avg_x,
                    'y_start': min_y,
                    'y_end': max_y
                },
                'length': max_y - min_y + 1,
                'thickness': avg_thickness,
                'merged_from': len(lines)
            }

    def _is_valid_line_position(self, line: Dict, max_dimension: int) -> bool:
        """
        Kontrola, zda je pozice linie validní (ne příliš blízko okrajům).
        """
        margin = 5  # minimální vzdálenost od okraje

        if line['type'] == 'horizontal':
            y_pos = line['coordinates']['y_position']
            return margin <= y_pos <= max_dimension - margin
        else:  # vertical
            x_pos = line['coordinates']['x_position']
            return margin <= x_pos <= max_dimension - margin

    def _find_line_intersections(self, horizontal_lines: List[Dict],
                                 vertical_lines: List[Dict]) -> List[Dict]:
        """
        Nalezení průsečíků mezi horizontálními a vertikálními liniemi.
        """
        intersections = []

        for h_line in horizontal_lines:
            for v_line in vertical_lines:
                # Kontrola, zda se linie protínají
                h_y = h_line['coordinates']['y_position']
                h_x_start = h_line['coordinates']['x_start']
                h_x_end = h_line['coordinates']['x_end']

                v_x = v_line['coordinates']['x_position']
                v_y_start = v_line['coordinates']['y_start']
                v_y_end = v_line['coordinates']['y_end']

                # Kontrola průsečíku
                if (h_x_start <= v_x <= h_x_end and
                        v_y_start <= h_y <= v_y_end):
                    intersections.append({
                        'point': (v_x, h_y),
                        'horizontal_line_id': id(h_line),
                        'vertical_line_id': id(v_line),
                        'horizontal_line': h_line,
                        'vertical_line': v_line
                    })

        return intersections

    def visualize_detected_lines(self, image: np.ndarray, detection_result: Dict) -> np.ndarray:
        """
        Vizualizace detekovaných linií pro debugging a kontrolu.
        """
        if len(image.shape) == 2:
            vis_img = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        else:
            vis_img = image.copy()

        # Kreslení horizontálních linií (červená)
        for line in detection_result['horizontal_lines']:
            start_point = line['coordinates']['start_point']
            end_point = line['coordinates']['end_point']
            cv2.line(vis_img, start_point, end_point, (0, 0, 255), 2)

            # Popisek s informacemi
            mid_x = (start_point[0] + end_point[0]) // 2
            mid_y = start_point[1]
            cv2.putText(vis_img, f"H:{line['length']}px",
                        (mid_x, mid_y - 5), cv2.FONT_HERSHEY_SIMPLEX,
                        0.4, (0, 0, 255), 1)

        # Kreslení vertikálních linií (zelená)
        for line in detection_result['vertical_lines']:
            start_point = line['coordinates']['start_point']
            end_point = line['coordinates']['end_point']
            cv2.line(vis_img, start_point, end_point, (0, 255, 0), 2)

            # Popisek s informacemi
            mid_x = start_point[0]
            mid_y = (start_point[1] + end_point[1]) // 2
            cv2.putText(vis_img, f"V:{line['length']}",
                        (mid_x + 5, mid_y), cv2.FONT_HERSHEY_SIMPLEX,
                        0.4, (0, 255, 0), 1)

        # Kreslení průsečíků (modrá)
        for intersection in detection_result['intersections']:
            point = intersection['point']
            cv2.circle(vis_img, point, 5, (255, 0, 0), -1)

        # Statistiky v rohu
        stats_text = [
            f"H-lines: {detection_result['stats']['horizontal_count']}",
            f"V-lines: {detection_result['stats']['vertical_count']}",
            f"Intersections: {detection_result['stats']['intersection_count']}"
        ]

        for i, text in enumerate(stats_text):
            cv2.putText(vis_img, text, (10, 25 + i * 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(vis_img, text, (10, 25 + i * 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

        return vis_img

    def export_coordinates_to_dict(self, detection_result: Dict) -> Dict:
        """
        Export koordinátů do strukturovaného slovníku pro další zpracování.
        """
        return {
            'horizontal_lines': [
                {
                    'id': i,
                    'y_position': line['coordinates']['y_position'],
                    'x_start': line['coordinates']['x_start'],
                    'x_end': line['coordinates']['x_end'],
                    'length': line['length'],
                    'thickness': line['thickness']
                }
                for i, line in enumerate(detection_result['horizontal_lines'])
            ],
            'vertical_lines': [
                {
                    'id': i,
                    'x_position': line['coordinates']['x_position'],
                    'y_start': line['coordinates']['y_start'],
                    'y_end': line['coordinates']['y_end'],
                    'length': line['length'],
                    'thickness': line['thickness']
                }
                for i, line in enumerate(detection_result['vertical_lines'])
            ],
            'intersections': [
                {
                    'x': intersection['point'][0],
                    'y': intersection['point'][1]
                }
                for intersection in detection_result['intersections']
            ]
        }