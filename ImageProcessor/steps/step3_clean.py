import cv2
import numpy as np

from ImageProcessor.steps.common import _ensure_odd


def sauvola_binarization(image: np.ndarray, window: int = 25, k: float = 0.2, R: float = 128.0) -> np.ndarray:
    window = _ensure_odd(window)
    img = image.astype(np.float32)
    mean = cv2.boxFilter(img, ddepth=-1, ksize=(window, window), normalize=True)
    sqmean = cv2.boxFilter(img * img, ddepth=-1, ksize=(window, window), normalize=True)
    var = np.maximum(sqmean - mean * mean, 0.0)
    std = np.sqrt(var)
    # Sauvola threshold
    thresh = mean * (1 + k * ((std / max(R, 1e-6)) - 1))
    # Binarize
    binary = (img > thresh).astype(np.uint8) * 255
    return binary

def median_filter(image: np.ndarray, ksize: int = 3) -> np.ndarray:
    ksize = _ensure_odd(ksize)
    return cv2.medianBlur(image, ksize)

# Tunable parameters for StepTuner (min, max, default)
sauvola_binarization.__tunable_params__ = {
    "window": (3, 101, 24),
    "k": (0.0, 1.0, 0.1),
    "R": (1.0, 256.0, 130.0),
}

median_filter.__tunable_params__ = {
    "ksize": (1, 21, 3),
}

def morphological_cleanup(image):
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    opened = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
    closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
    return closed


# --- Adapter functions for StepTuner (img, params_dict) -> BGR visualization ---

def tune_sauvola_binarization(img: np.ndarray, p: dict) -> np.ndarray:
    window = int(p.get("window", 25))
    k = float(p.get("k", 0.2))
    R = float(p.get("R", 128.0))
    out = sauvola_binarization(img, window=window, k=k, R=R)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)


def tune_median_filter(img: np.ndarray, p: dict) -> np.ndarray:
    ksize = int(p.get("ksize", 3))
    out = median_filter(img, ksize=ksize)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)

# Re-use underlying tuning specs
try:
    tune_sauvola_binarization.__tunable_params__ = sauvola_binarization.__tunable_params__
    tune_median_filter.__tunable_params__ = median_filter.__tunable_params__
except Exception:
    pass
