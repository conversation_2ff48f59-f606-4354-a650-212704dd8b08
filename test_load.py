#!/usr/bin/env python3

import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/OcrPipeline')

from ImageImporter.image_importer import FileImporter

def test_load():
    print("Testování načítání souborů...")
    
    importer = FileImporter("hotfolder")
    
    # Test existence složky
    print(f"Složka existuje: {os.path.exists('hotfolder')}")
    
    # Výpis souborů ve složce
    files = importer.get_files_in_folder()
    print(f"Nalezené soubory: {files}")
    
    # Test načtení konkrétního souboru
    print("\nTestování načtení F8.pdf:")
    result = importer.load_file("F8.pdf")
    print(f"Výsledek: {type(result)}, délka: {len(result) if result else 'None'}")
    
    if result:
        img = result[0]
        print(f"Ob<PERSON><PERSON><PERSON><PERSON>: {type(img)}, rozm<PERSON>ry: {img.shape if hasattr(img, 'shape') else 'N/A'}")
    
    # Test jiného souboru
    print("\nTestování načtení F1.pdf:")
    result2 = importer.load_file("F1.pdf")
    print(f"Výsledek: {type(result2)}, délka: {len(result2) if result2 else 'None'}")

if __name__ == "__main__":
    test_load()
