import cv2

from ImageImporter.image_importer import FileImporter
from ImageProcessor.image_processor import ImageProcessor
from OcrEngine.ocr_engine import OcrEngine


class OcrPipeline:
    def __init__(self):
        self.file_importer = FileImporter('hotfolder')

        self.image_processor = ImageProcessor()
        self.ocr_engine = OcrEngine()

        #self.post_processor = PostProcessor(ocr_engine=self.ocr_engine)

        self.image = None


    def process(self):

        file = 'square'
        file_name = file + '.PDF'

        self.image = self.file_importer.load_file(file_name)[0]
        self.image = self.image_processor.run(self.image)
        cv2.imshow('image', self.image)
        cv2.waitKey(0)
        cv2.imwrite('output/' + file + '.png', self.image)
        #df = self.ocr_engine.process_data(self.image)

        return


if __name__ == "__main__":
    pipeline = OcrPipeline()
    pipeline.process()