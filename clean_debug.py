#!/usr/bin/env python3
"""
Skript pro vyčištění debug výstupů.
"""

import os
import shutil
import argparse


def clean_debug_folders():
    """Vyčistí debug složky."""
    folders_to_clean = [
        "debug_output",
        "debug_single_output", 
        "test_debug"
    ]
    
    cleaned_count = 0
    
    for folder in folders_to_clean:
        if os.path.exists(folder):
            print(f"🗑️  Mažu složku: {folder}")
            shutil.rmtree(folder)
            cleaned_count += 1
        else:
            print(f"⚠️  Složka neexistuje: {folder}")
    
    if cleaned_count > 0:
        print(f"\n✅ Vyčištěno {cleaned_count} debug složek")
    else:
        print("\n💡 Žádné debug složky k vyčištění")


def main():
    """Hlavní funkce skriptu."""
    parser = argparse.ArgumentParser(
        description="Vyčištění debug výstupů",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--confirm", "-y",
        action="store_true",
        help="Potvrdit mazání bez dotazu"
    )
    
    args = parser.parse_args()
    
    print("🧹 Skript pro vyčištění debug výstupů")
    print("=" * 40)
    
    if not args.confirm:
        response = input("Opravdu chcete smazat všechny debug složky? (y/N): ")
        if response.lower() not in ['y', 'yes', 'ano']:
            print("❌ Operace zrušena")
            return
    
    clean_debug_folders()


if __name__ == "__main__":
    main()
