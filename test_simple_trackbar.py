#!/usr/bin/env python3

import cv2
import numpy as np
import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/OcrPipeline')

from ImageImporter.image_importer import FileImporter
from ImageProcessor.steps.step1_prepare import to_grayscale

def simple_process(img, params):
    """Jednoduchá funkce pro test trackbaru"""
    vis = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
    
    # Zobraz parametry na obrázku
    y_pos = 30
    for name, value in params.items():
        text = f"{name}: {value}"
        cv2.putText(vis, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_pos += 30
    
    # Nak<PERSON>li testovací obdélník
    x = int(params.get("X pos", 100))
    y = int(params.get("Y pos", 100))
    w = int(params.get("Width", 200))
    h = int(params.get("Height", 100))
    
    cv2.rectangle(vis, (x, y), (x + w, y + h), (0, 0, 255), 3)
    
    return vis

def test_trackbar():
    print("Test jednoduchého trackbaru...")
    
    # Načti obrázek
    importer = FileImporter("hotfolder")
    img = importer.load_file("F8.pdf")[0]
    img = to_grayscale(img)
    
    # Zmenši obrázek pro lepší zobrazení
    img = cv2.resize(img, (800, 600))
    print(f"Obrázek zmenšen na: {img.shape}")
    
    # Jednoduché parametry připojené k funkci (min, max, default)
    simple_process.__tunable_params__ = {
        "X pos": (0, 700, 100),
        "Y pos": (0, 500, 100),
        "Width": (10, 400, 200),
        "Height": (10, 200, 100),
        "Float test": (0.1, 2.0, 0.5),  # Float parametr
    }

    from Tuner.step_tuner import StepTuner
    tuner = StepTuner("Test Trackbar", simple_process)
    tuner.run(img)

if __name__ == "__main__":
    test_trackbar()
