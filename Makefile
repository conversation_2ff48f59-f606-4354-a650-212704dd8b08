# Makefile pro debug pipeline

.PHONY: help debug-all debug-single clean test

# Vý<PERSON>zí cíl
help:
	@echo "🚀 Debug Pipeline - Dostupné příkazy:"
	@echo ""
	@echo "  make debug-all     - Spustí debug pipeline na všech PDF souborech"
	@echo "  make debug-single  - Spustí debug pipeline na konkrétním souboru (FILE=název.pdf)"
	@echo "  make clean         - Vyčistí všechny debug výstupy"
	@echo "  make test          - Spustí test na F1.pdf"
	@echo ""
	@echo "Příklady:"
	@echo "  make debug-single FILE=F1.pdf"
	@echo "  make debug-single FILE=stamps.pdf"

# Spuštění debug pipeline na všech PDF souborech
debug-all:
	@echo "🚀 Spouštím debug pipeline na všech PDF souborech..."
	python3 run_debug.py

# Spuštění debug pipeline na konkrétním souboru
debug-single:
	@if [ -z "$(FILE)" ]; then \
		echo "❌ Chyba: Musíte zadat FILE=název.pdf"; \
		echo "Příklad: make debug-single FILE=F1.pdf"; \
		exit 1; \
	fi
	@echo "🚀 Spouštím debug pipeline na souboru: $(FILE)"
	python3 run_debug_single.py "$(FILE)" --output-dir debug_single_output

# Vyčištění debug výstupů
clean:
	@echo "🧹 Čistím debug výstupy..."
	python3 clean_debug.py --confirm

# Test na F1.pdf
test:
	@echo "🧪 Spouštím test na F1.pdf..."
	python3 run_debug_single.py F1.pdf --output-dir test_debug
