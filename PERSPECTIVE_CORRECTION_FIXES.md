# Opravy funkce correct_perspective

## Provedené změny

### 1. **<PERSON><PERSON><PERSON><PERSON> sjedn<PERSON> s tune_correct_perspective**

<PERSON>ce `correct_perspective` byla upravena tak, aby byla funkčně shodná s `tune_correct_perspective`, ale skutečně aplikovala perspektivní transformaci místo pouhé vizualizace.

### 2. **Opravené chyby**

#### a) Chyba v výpočtu bodů na linii
**Před opravou:**
```python
y0 = b * theta  # CHYBA - nesprávný výpočet
```

**Po opravě:**
```python
y0 = b * rho    # SPRÁVNĚ - správný výpočet
```

#### b) Odstraněn duplicitní kód
- Odstraněn duplicitní blok filtrování linií (řádky 210-268)
- Zjednodušena logika bez ztráty funkčnosti

#### c) Opraveny importy
**Před opravou:**
```python
from numpy.distutils.system_info import p  # Problematický import
```

**Po opravě:**
```python
# Import odstraněn - nebyl potřebný
```

### 3. **Parametrizace**

Funkce nyní používá stejné výchozí parametry jako `tune_correct_perspective`:

```python
blur_ksize = 11          # Velikost Gaussian blur kernelu
canny_low = 90           # Dolní práh pro Canny edge detection
canny_high = 130         # Horní práh pro Canny edge detection
hough_threshold = 180    # Práh pro HoughLines
angle_tol_deg = 15.0     # Tolerance úhlu pro filtrování linií
min_horiz_len = 1800.0   # Minimální délka horizontálních linií
min_vert_len = 2800.0    # Minimální délka vertikálních linií
```

### 4. **Aktualizované tunable parametry**

```python
correct_perspective.__tunable_params__ = {
    "blur_ksize": (3, 31, 11),
    "canny_low": (10, 200, 90),
    "canny_high": (50, 300, 130),
    "hough_threshold": (50, 400, 180),
    "min_horiz_len": (0, 2500, 1800),
    "min_vert_len": (0, 4000, 2800),
    "angle_tol_deg": (1.0, 45.0, 15.0),
}
```

## Algoritmus funkce

1. **Příprava obrazu**
   - Konverze na grayscale (pokud je barevný)
   - Gaussian blur pro redukci šumu

2. **Detekce hran**
   - Canny edge detection

3. **Detekce linií**
   - HoughLines pro nalezení přímek

4. **Filtrování linií**
   - Filtrování podle úhlu (pouze horizontální a vertikální)
   - Filtrování podle délky (odstranění krátkých linií)

5. **Klasifikace linií**
   - Rozdělení na horizontální a vertikální
   - Výběr krajních linií (top, bottom, left, right)

6. **Výpočet rohů**
   - Průsečíky krajních linií = 4 rohy dokumentu

7. **Perspektivní transformace**
   - Výpočet cílových rozměrů
   - Aplikace perspektivní transformace

## Testování

Funkce byla úspěšně otestována:

```bash
# Test základní funkčnosti
python3 -c "from ImageProcessor.steps.step1_prepare import correct_perspective; ..."

# Test s debug pipeline
python3 run_debug_single.py square.pdf --output-dir test_corrected_perspective
```

## Výsledek

✅ Funkce `correct_perspective` je nyní funkčně shodná s `tune_correct_perspective`
✅ Opraveny všechny identifikované chyby
✅ Zachována kompatibilita s existujícím kódem
✅ Přidána podpora pro tunable parametry
✅ Funkce úspěšně prošla testy
