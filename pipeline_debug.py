import os
import cv2
import numpy as np
from skimage.filters import threshold_sauvola, threshold_otsu
import math


class PreprocessPipelineDebug:
    def __init__(self, debug=False, output_debug_path=None):
        self.debug = debug
        self.output_debug_path = output_debug_path
        self.step_counter = 0

    def _save_debug_image(self, image, step_name):
        """Uloží debug obrázek s příslušným názvem"""
        if not self.debug:
            return

        filename = f"{self.step_counter:02d}_{step_name}.png"
        filepath = os.path.join(self.output_debug_path, filename)
        cv2.imwrite(filepath, image)
        self.step_counter += 1

    def process(self, image_path, input_dpi=None, **kwargs):
        """Hlavní metoda pro zpracování obrazu"""
        # Načtení vstupního obrázku
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Nelze načíst obráze<PERSON> z {image_path}")

        # Vytvoření výstupní složky pro debug obrázky
        if self.debug:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            self.output_debug_path = os.path.join(
                os.path.dirname(image_path) if self.output_debug_path is None else self.output_debug_path,
                f"{base_name}.debug"
            )
            os.makedirs(self.output_debug_path, exist_ok=True)
            self.step_counter = 0

        # Fáze 1: Geometrická a globální příprava
        # Krok 1: Konverze do stupňů šedi
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        self._save_debug_image(gray, "grayscale")

        # Krok 2: Normalizace DPI
        if input_dpi and input_dpi != 300:
            scale_factor = 300.0 / input_dpi
            new_width = int(gray.shape[1] * scale_factor)
            new_height = int(gray.shape[0] * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        self._save_debug_image(gray, "normalized_dpi")

        # Krok 3: Detekce úhlu sklonu
        # angle = self._detect_skew_angle(gray.copy())

        # Krok 4: Aplikace rotace
        # deskewed = self._rotate_image(gray, angle)
        # self._save_debug_image(deskewed, "deskewed")

        # Fáze 2: Vylepšení kvality obrazu
        # Krok 5: Odstranění šumu
        denoised = cv2.fastNlMeansDenoising(
            gray,
            h=kwargs.get('denoise_h', 10),
            templateWindowSize=kwargs.get('denoise_template_size', 7),
            searchWindowSize=kwargs.get('denoise_search_size', 21)
        )
        self._save_debug_image(denoised, "denoised")

        # Krok 6: CLAHE
        clahe = cv2.createCLAHE(
            clipLimit=kwargs.get('clahe_clip_limit', 5.0),
            tileGridSize=(kwargs.get('clahe_grid_size', 10), kwargs.get('clahe_grid_size', 10))
        )
        clahe_applied = clahe.apply(denoised)
        self._save_debug_image(clahe_applied, "clahe")

        # Krok 7: Doostření (volitelné)
        if kwargs.get('apply_unsharp_mask', False):
            unsharp = self._unsharp_mask(
                clahe_applied,
                kernel_size=kwargs.get('unsharp_kernel_size', 5),
                sigma=kwargs.get('unsharp_sigma', 1.0),
                amount=kwargs.get('unsharp_amount', 1.0),
                threshold=kwargs.get('unsharp_threshold', 0)
            )
            self._save_debug_image(unsharp, "unsharp_mask")
        else:
            unsharp = clahe_applied

        # Fáze 3: Binarizace a finální čištění
        # Krok 8: Binarizace Sauvola
        sauvola_threshold = threshold_sauvola(
            unsharp,
            window_size=kwargs.get('sauvola_window_size', 25),
            k=kwargs.get('sauvola_k', 0.2),
            r=kwargs.get('sauvola_r', 128)
        )
        binary = (unsharp > sauvola_threshold).astype(np.uint8) * 255
        self._save_debug_image(binary, "sauvola_binarization")

        # Krok 9: Mediánový filtr
        median = cv2.medianBlur(binary, kwargs.get('median_kernel_size', 3))
        self._save_debug_image(median, "median_filter")

        # Krok 10: Morfologické operace (volitelné)
        if kwargs.get('apply_morphology', False):
            morph = self._morphological_operations(
                median,
                kernel_size=kwargs.get('morph_kernel_size', 2)
            )
            self._save_debug_image(morph, "morphological_operations")
        else:
            morph = median

        return morph

    def _detect_skew_angle(self, image):
        """Detekce úhlu sklonu pomocí dočasné binarizace"""
        # Normalizace kontrastu
        normalized = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX)

        # Binarizace Otsu
        _, binary = cv2.threshold(normalized, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Detekce přímek pomocí Houghovy transformace
        lines = cv2.HoughLinesP(
            binary, 1, np.pi / 180, threshold=100, minLineLength=100, maxLineGap=10
        )

        if lines is None:
            return 0.0

        # Výpočet úhlů všech detekovaných čar
        angles = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            angle = math.degrees(math.atan2(y2 - y1, x2 - x1))
            angles.append(angle)

        # Výpočet mediánu úhlů
        median_angle = np.median(angles)

        # Omezení úhlu na rozumný rozsah
        if median_angle < -45:
            median_angle += 90
        elif median_angle > 45:
            median_angle -= 90

        return median_angle

    def _rotate_image(self, image, angle):
        """Rotace obrázku o zadaný úhel"""
        if abs(angle) < 0.1:  # Ignorovat velmi malé úhly
            return image

        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # Vytvoření transformační matice
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # Aplikace rotace
        rotated = cv2.warpAffine(
            image, rotation_matrix, (width, height),
            flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE
        )

        return rotated

    def _unsharp_mask(self, image, kernel_size=5, sigma=1.0, amount=1.0, threshold=0):
        """Aplikace unsharp mask pro doostření obrazu"""
        # Rozmazání obrazu
        blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)

        # Výpočet masky
        sharpened = float(amount + 1) * image - float(amount) * blurred

        # Oříznutí hodnot na platný rozsah
        sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)

        # Aplikace prahování (volitelné)
        if threshold > 0:
            low_contrast_mask = np.abs(image - blurred) < threshold
            np.copyto(sharpened, image, where=low_contrast_mask)

        return sharpened

    def _morphological_operations(self, image, kernel_size=2):
        """Morfologické operace pro vyčištění obrázku"""
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # Otevření pro odstranění malých šumů
        cleaned = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)

        # Uzavření pro spojení přerušených částí textu
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel)

        return cleaned


# Příklad použití
if __name__ == "__main__":
    # Vytvoření pipeline s povoleným debug režimem
    pipeline = PreprocessPipelineDebug(debug=True)

    # Zpracování obrázku
    try:
        result = pipeline.process(
            "vstupni_obrazek.jpg",
            input_dpi=150,  # Pokud známe vstupní DPI
            denoise_h=12,
            clahe_clip_limit=3.0,
            clahe_grid_size=16,
            sauvola_window_size=35,
            sauvola_k=0.3,
            apply_unsharp_mask=True,
            unsharp_amount=0.8,
            apply_morphology=True
        )

        # Uložení výsledného obrázku
        cv2.imwrite("vystupni_obrazek.jpg", result)
        print("Zpracování dokončeno úspěšně.")

    except Exception as e:
        print(f"Chyba při zpracování: {e}")