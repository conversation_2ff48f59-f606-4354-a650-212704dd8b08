# Debug Pipeline - Návod k použití

Tento projekt obsahuje debug verzi preprocessing pipeline, která umožňuje sledovat každý krok zpracování obrazu a ukládat mezivýsledky pro analýzu.

## Soubory

### `pipeline_debug.py`
- **<PERSON><PERSON><PERSON><PERSON> `PreprocessPipelineDebug`** - hlavní debug pipeline
- Zpracovává obrázky krok za krokem a ukládá mezivýsledky
- Podporuje nastavitelné parametry pro každý krok

### `run_debug.py`
- **Skript pro batch zpracování** všech PDF souborů v hotfolder
- Automaticky zpracuje všechny PDF soubory a vytvoří debug výstupy

### `run_debug_single.py`
- **Skript pro zpracování konkrétního souboru** s možností nastavení parametrů
- Umožňuje jemné ladění parametrů pro konkrétní dokument

### `clean_debug.py`
- **Skript pro vyčištění debug výstupů**
- Smaže všechny debug složky (debug_output, debug_single_output, test_debug)

## Použití

### 1. Batch zpracování všech PDF souborů

```bash
python run_debug.py
```

Tento příkaz:
- Najde všechny PDF soubory ve složce `hotfolder/`
- Zpracuje každý soubor pomocí debug pipeline
- Vytvoří složku `debug_output/` s výsledky
- Pro každou stránku vytvoří:
  - Podsložku s debug obrázky jednotlivých kroků
  - Finální výsledek s příponou `_final.png`

### 2. Zpracování konkrétního souboru

```bash
python run_debug_single.py F1.pdf
```

Základní použití s výchozími parametry.

#### Pokročilé použití s vlastními parametry:

```bash
python run_debug_single.py F1.pdf \
    --output-dir my_debug_output \
    --input-dpi 300 \
    --denoise-h 15 \
    --clahe-clip-limit 3.0 \
    --sauvola-window-size 35 \
    --apply-unsharp-mask \
    --apply-morphology
```

#### Dostupné parametry:

**Základní:**
- `--output-dir` - výstupní složka (výchozí: `debug_single_output`)
- `--input-dpi` - vstupní DPI PDF (výchozí: 200)

**Denoising:**
- `--denoise-h` - síla denoising filtru (výchozí: 10)
- `--denoise-template-size` - velikost template okna (výchozí: 7)
- `--denoise-search-size` - velikost search okna (výchozí: 21)

**CLAHE:**
- `--clahe-clip-limit` - clip limit (výchozí: 2.0)
- `--clahe-grid-size` - velikost gridu (výchozí: 8)

**Sauvola binarizace:**
- `--sauvola-window-size` - velikost okna (výchozí: 25)
- `--sauvola-k` - K parametr (výchozí: 0.2)
- `--sauvola-r` - R parametr (výchozí: 128)

**Mediánový filtr:**
- `--median-kernel-size` - velikost kernelu (výchozí: 3)

**Unsharp mask (volitelné):**
- `--apply-unsharp-mask` - zapnout unsharp mask
- `--unsharp-kernel-size` - velikost kernelu (výchozí: 5)
- `--unsharp-sigma` - sigma (výchozí: 1.0)
- `--unsharp-amount` - amount (výchozí: 1.0)
- `--unsharp-threshold` - threshold (výchozí: 0)

**Morfologické operace (volitelné):**
- `--apply-morphology` - zapnout morfologické operace
- `--morph-kernel-size` - velikost kernelu (výchozí: 2)

### 3. Nápověda

```bash
python run_debug_single.py --help
```

### 4. Vyčištění debug výstupů

```bash
python clean_debug.py
```

Nebo bez potvrzení:

```bash
python clean_debug.py --confirm
```

## Použití s Makefile (doporučeno)

Pro snadnější použití je k dispozici Makefile:

```bash
# Zobrazení nápovědy
make help

# Spuštění debug pipeline na všech PDF souborech
make debug-all

# Spuštění debug pipeline na konkrétním souboru
make debug-single FILE=F1.pdf
make debug-single FILE=stamps.pdf

# Test na F1.pdf
make test

# Vyčištění debug výstupů
make clean
```

## Struktura výstupů

Po spuštění debug pipeline najdete následující strukturu:

```
debug_output/
├── filename_page_01_debug/
│   ├── 01_grayscale.png
│   ├── 02_normalized_dpi.png
│   ├── 03_denoised.png
│   ├── 04_clahe.png
│   ├── 05_unsharp_mask.png (pokud zapnuto)
│   ├── 06_sauvola_binarization.png
│   ├── 07_median_filter.png
│   └── 08_morphological_operations.png (pokud zapnuto)
├── filename_page_01_final.png
├── filename_page_02_debug/
│   └── ... (stejná struktura)
└── filename_page_02_final.png
```

## Kroky zpracování

Debug pipeline provádí následující kroky:

1. **Grayscale** - konverze do stupňů šedi
2. **Normalized DPI** - normalizace na 300 DPI
3. **Denoised** - odstranění šumu pomocí Non-local Means
4. **CLAHE** - adaptivní vyrovnání histogramu
5. **Unsharp Mask** - doostření (volitelné)
6. **Sauvola Binarization** - adaptivní binarizace
7. **Median Filter** - mediánový filtr pro vyhlazení
8. **Morphological Operations** - morfologické operace (volitelné)

## Tipy pro ladění parametrů

1. **Pro dokumenty s nízkým kontrastem:** zvyšte `clahe-clip-limit`
2. **Pro zašuměné dokumenty:** zvyšte `denoise-h`
3. **Pro dokumenty s malým textem:** snižte `sauvola-window-size`
4. **Pro rozmazané dokumenty:** zapněte `--apply-unsharp-mask`
5. **Pro dokumenty s artefakty:** zapněte `--apply-morphology`

## Požadavky

- Python 3.7+
- OpenCV (`cv2`)
- NumPy
- scikit-image (`skimage`)
- pdf2image (pro načítání PDF)

## Instalace závislostí

```bash
pip install opencv-python numpy scikit-image pdf2image
```
