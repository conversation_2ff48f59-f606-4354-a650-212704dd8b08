#!/usr/bin/env python3
"""
Skript pro spuštění debug pipeline na všech PDF souborech ve složce hotfolder.
Vytváří debug obrázky pro každý krok zpracování.
"""

import os
import sys
import cv2
from pathlib import Path
from typing import List

# Přidání cesty k projektu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ImageImporter.image_importer import FileImporter
from pipeline_debug import PreprocessPipelineDebug


def get_pdf_files(folder_path: str) -> List[str]:
    """
    Najde všechny PDF soubory ve složce.
    
    Args:
        folder_path: Cesta ke složce
        
    Returns:
        Seznam názvů PDF souborů
    """
    if not os.path.exists(folder_path):
        print(f"Složka neexistuje: {folder_path}")
        return []
    
    pdf_files = []
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.pdf'):
            pdf_files.append(filename)
    
    return sorted(pdf_files)


def process_single_pdf(pdf_filename: str, hotfolder_path: str, output_base_path: str):
    """
    Zpracuje jeden PDF soubor pomocí debug pipeline.
    
    Args:
        pdf_filename: Název PDF souboru
        hotfolder_path: Cesta k hotfolder
        output_base_path: Základní cesta pro výstupní soubory
    """
    print(f"\n{'='*60}")
    print(f"Zpracovávám: {pdf_filename}")
    print(f"{'='*60}")
    
    # Načtení PDF pomocí FileImporter
    importer = FileImporter(hotfolder_path)
    
    try:
        images = importer.load_file(pdf_filename)
        if not images:
            print(f"❌ Nepodařilo se načíst PDF: {pdf_filename}")
            return False
            
        print(f"✅ Načteno {len(images)} stránek z PDF")
        
        # Zpracování každé stránky
        for page_idx, image in enumerate(images):
            page_name = f"{Path(pdf_filename).stem}_page_{page_idx + 1:02d}"
            print(f"\n📄 Zpracovávám stránku {page_idx + 1}/{len(images)}: {page_name}")
            
            # Vytvoření dočasného souboru pro debug pipeline
            temp_image_path = os.path.join(output_base_path, f"{page_name}_temp.png")
            cv2.imwrite(temp_image_path, image)
            
            # Vytvoření debug pipeline
            debug_output_path = os.path.join(output_base_path, f"{page_name}_debug")
            pipeline = PreprocessPipelineDebug(
                debug=True, 
                output_debug_path=debug_output_path
            )
            
            try:
                # Spuštění debug pipeline s různými parametry
                result = pipeline.process(
                    temp_image_path,
                    input_dpi=300,
                    
                    # Parametry pro denoising
                    denoise_h=10,
                    denoise_template_size=7,
                    denoise_search_size=21,
                    
                    # Parametry pro CLAHE
                    clahe_clip_limit=5.0,
                    clahe_grid_size=10,
                    
                    # Parametry pro Sauvola binarizaci
                    sauvola_window_size=25,
                    sauvola_k=0.1,
                    sauvola_r=120,
                    
                    # Parametry pro mediánový filtr
                    median_kernel_size=3,
                    
                    # Volitelné parametry
                    apply_unsharp_mask=True,
                    unsharp_kernel_size=5,
                    unsharp_sigma=1.0,
                    unsharp_amount=1.0,
                    unsharp_threshold=0,
                    
                    apply_morphology=True,
                    morph_kernel_size=2
                )
                
                # Uložení finálního výsledku
                final_output_path = os.path.join(output_base_path, f"{page_name}_final.png")
                cv2.imwrite(final_output_path, result)
                
                print(f"✅ Stránka zpracována úspěšně")
                print(f"   📁 Debug obrázky: {debug_output_path}")
                print(f"   🖼️  Finální výsledek: {final_output_path}")
                
            except Exception as e:
                print(f"❌ Chyba při zpracování stránky {page_idx + 1}: {e}")
                
            finally:
                # Smazání dočasného souboru
                if os.path.exists(temp_image_path):
                    os.remove(temp_image_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při načítání PDF {pdf_filename}: {e}")
        return False


def main():
    """Hlavní funkce skriptu."""
    print("🚀 Spouštím debug pipeline pro všechny PDF soubory v hotfolder")
    print("=" * 70)
    
    # Nastavení cest
    hotfolder_path = "hotfolder"
    output_base_path = "debug_output"
    
    # Vytvoření výstupní složky
    os.makedirs(output_base_path, exist_ok=True)
    
    # Najití všech PDF souborů
    pdf_files = get_pdf_files(hotfolder_path)
    
    if not pdf_files:
        print("❌ Ve složce hotfolder nebyly nalezeny žádné PDF soubory")
        return
    
    print(f"📋 Nalezeno {len(pdf_files)} PDF souborů:")
    for i, filename in enumerate(pdf_files, 1):
        print(f"   {i:2d}. {filename}")
    
    # Zpracování všech PDF souborů
    successful_count = 0
    failed_count = 0
    
    for pdf_file in pdf_files:
        success = process_single_pdf(pdf_file, hotfolder_path, output_base_path)
        if success:
            successful_count += 1
        else:
            failed_count += 1
    
    # Souhrn
    print(f"\n{'='*70}")
    print("📊 SOUHRN ZPRACOVÁNÍ")
    print(f"{'='*70}")
    print(f"✅ Úspěšně zpracováno: {successful_count} souborů")
    print(f"❌ Chyby při zpracování: {failed_count} souborů")
    print(f"📁 Výstupní složka: {os.path.abspath(output_base_path)}")
    
    if successful_count > 0:
        print(f"\n💡 Pro každý zpracovaný soubor najdete:")
        print(f"   • Debug obrázky jednotlivých kroků v podsložkách")
        print(f"   • Finální výsledek s příponou '_final.png'")


if __name__ == "__main__":
    main()
